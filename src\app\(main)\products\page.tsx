import React from 'react';
import type { Metadata } from 'next';
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';
import ProductsHeroSection from '@/components/products/hero-section';
import InteractiveModelSection from '@/components/products/interactive-model-section';

export async function generateMetadata(): Promise<Metadata> {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'products');

  return {
    title: t('site.title'),
    description: t('site.description'),
    openGraph: {
      title: t('site.title'),
      description: t('site.description'),
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('site.title'),
      description: t('site.description'),
    },
  };
}

const ProductsPage = () => {
  return (
    <main className="min-h-screen">
      <ProductsHeroSection />
      <InteractiveModelSection />
    </main>
  );
};

export default ProductsPage;
