'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'motion/react';
import { useTranslation, Trans } from 'react-i18next';
import {
  Search,
  MapPin,
  Clock,
  Building,
  ArrowRight,
  X,
  Filter,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const JobListings = () => {
  const { t } = useTranslation('careers');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');

  // Job data from translations
  const jobKeys = ['senior_frontend', 'backend_engineer', 'product_manager'];
  const jobs = useMemo(
    () =>
      jobKeys.map((key) => {
        const requirements = t(`positions.jobs.${key}.requirements`, {
          returnObjects: true,
        });

        return {
          id: key,
          title: t(`positions.jobs.${key}.title`),
          department: t(`positions.jobs.${key}.department`),
          location: t(`positions.jobs.${key}.location`),
          type: t(`positions.jobs.${key}.type`),
          description: t(`positions.jobs.${key}.description`),
          requirements: Array.isArray(requirements) ? requirements : [],
        };
      }),
    [t]
  );

  // Filter options
  const departments = [
    'all',
    'engineering',
    'product',
    'design',
    'sales',
    'marketing',
    'support',
  ];

  // Filtered jobs
  const filteredJobs = useMemo(() => {
    let filtered = jobs;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (job) =>
          job.title.toLowerCase().includes(query) ||
          job.department.toLowerCase().includes(query) ||
          job.description.toLowerCase().includes(query)
      );
    }

    // Filter by department
    if (selectedDepartment !== 'all') {
      const departmentName = t(`positions.filters.${selectedDepartment}`);
      filtered = filtered.filter((job) => job.department === departmentName);
    }

    return filtered;
  }, [jobs, searchQuery, selectedDepartment, t]);

  const clearSearch = () => {
    setSearchQuery('');
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedDepartment('all');
  };

  return (
    <section id="job-listings" className="relative px-4 py-20 lg:py-32">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="from-accent/5 to-primary/5 absolute top-1/3 left-1/4 h-72 w-72 rounded-full bg-gradient-to-r blur-3xl" />
        <div className="from-primary/5 to-secondary/5 absolute right-1/4 bottom-1/3 h-72 w-72 rounded-full bg-gradient-to-r blur-3xl" />
      </div>

      <div className="container mx-auto max-w-6xl">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{
            staggerChildren: 0.1,
            delayChildren: 0.2,
          }}
          className="space-y-12"
        >
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="text-center"
          >
            <div className="space-y-6">
              <motion.h2
                className="text-3xl font-bold md:text-4xl lg:text-5xl"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Trans
                  i18nKey="positions.title"
                  ns="careers"
                  components={{
                    highlighted: (
                      <span className="from-primary via-secondary to-accent bg-gradient-to-r bg-clip-text text-transparent" />
                    ),
                  }}
                />
              </motion.h2>

              <motion.p
                className="text-muted-foreground mx-auto max-w-2xl text-xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {t('positions.subtitle')}
              </motion.p>

              <motion.p
                className="text-muted-foreground mx-auto max-w-3xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                {t('positions.description')}
              </motion.p>
            </div>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
              delay: 0.2,
            }}
            className="space-y-6"
          >
            {/* Search Bar */}
            <div className="relative mx-auto max-w-2xl">
              <Search className="text-muted-foreground absolute top-1/2 left-4 h-5 w-5 -translate-y-1/2 transform" />
              <Input
                type="text"
                placeholder={t('positions.search_placeholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-card/80 border-border/50 focus:border-primary/50 focus:ring-primary/20 h-14 pr-12 pl-12 text-lg backdrop-blur-sm transition-all duration-300"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="hover:bg-muted/80 absolute top-1/2 right-2 h-8 w-8 -translate-y-1/2 transform p-0"
                  aria-label="Clear search"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Department Filters */}
            <div className="flex flex-wrap justify-center gap-2">
              {departments.map((dept) => (
                <Button
                  key={dept}
                  variant={selectedDepartment === dept ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedDepartment(dept)}
                  className={`transition-all duration-200 ${
                    selectedDepartment === dept
                      ? 'from-primary to-secondary bg-gradient-to-r text-white'
                      : 'hover:border-primary/50 hover:bg-primary/5'
                  }`}
                >
                  {t(`positions.filters.${dept}`)}
                </Button>
              ))}
            </div>

            {/* Clear Filters */}
            {(searchQuery || selectedDepartment !== 'all') && (
              <div className="flex justify-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <Filter className="mr-2 h-4 w-4" />
                  Clear all filters
                </Button>
              </div>
            )}
          </motion.div>

          {/* Job Cards */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{
              staggerChildren: 0.1,
              delayChildren: 0.2,
            }}
            className="space-y-6"
          >
            {filteredJobs.length > 0 ? (
              filteredJobs.map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{
                    duration: 0.6,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    delay: index * 0.1,
                  }}
                  whileHover={{
                    y: -4,
                    scale: 1.01,
                    transition: { duration: 0.3, ease: 'easeOut' },
                  }}
                  className="group"
                >
                  <Card className="bg-card/80 border-border/50 group-hover:border-primary/30 backdrop-blur-sm transition-all duration-300 hover:shadow-xl">
                    <CardHeader className="pb-4">
                      <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
                        <div className="space-y-2">
                          <CardTitle className="text-xl">{job.title}</CardTitle>
                          <div className="flex flex-wrap gap-2">
                            <Badge
                              variant="secondary"
                              className="gap-1 text-white"
                            >
                              <Building className="h-3 w-3" />
                              {job.department}
                            </Badge>
                            <Badge variant="outline" className="gap-1">
                              <MapPin className="h-3 w-3" />
                              {job.location}
                            </Badge>
                            <Badge variant="outline" className="gap-1">
                              <Clock className="h-3 w-3" />
                              {job.type}
                            </Badge>
                          </div>
                        </div>
                        <Button
                          className="from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 bg-gradient-to-r text-white transition-all duration-300 hover:shadow-lg"
                          asChild
                        >
                          <a
                            href={`mailto:<EMAIL>?subject=Application for ${job.title}`}
                          >
                            Apply Now
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </a>
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-muted-foreground leading-relaxed">
                        {job.description}
                      </p>
                      {job.requirements && job.requirements.length > 0 && (
                        <div className="space-y-2">
                          <h4 className="text-foreground font-medium">
                            Requirements:
                          </h4>
                          <ul className="text-muted-foreground space-y-1 text-sm">
                            {job.requirements.map((req, reqIndex) => (
                              <li
                                key={reqIndex}
                                className="flex items-start gap-2"
                              >
                                <span className="text-primary mt-1.5 h-1 w-1 flex-shrink-0 rounded-full bg-current" />
                                {req}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="py-12 text-center"
              >
                <div className="text-muted-foreground space-y-2">
                  <p className="text-lg">{t('positions.no_results')}</p>
                  <Button variant="ghost" onClick={clearFilters}>
                    Clear filters to see all positions
                  </Button>
                </div>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default JobListings;
