'use client';

import React from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import { FileText, Zap, Shield, TrendingUp } from 'lucide-react';
import GradientHighlighter from '@/components/gradient-highlighter';

const ProductsHeroSection = () => {
  const { t } = useTranslation('products');

  return (
    <section className="relative min-h-[80vh] overflow-hidden bg-gradient-to-br from-background via-muted/30 to-background">
      {/* Background decorative elements */}
      <div className="pointer-events-none absolute inset-0">
        {/* Animated gradient orbs */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.3, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute -top-1/4 -left-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br from-primary/40 to-secondary/30 blur-3xl dark:from-primary/20 dark:to-secondary/15"
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.25, scale: 1 }}
          transition={{ duration: 2, delay: 0.5 }}
          className="absolute -bottom-1/4 -right-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br from-accent/30 to-primary/20 blur-3xl dark:from-accent/15 dark:to-primary/10"
        />

        {/* Floating document icons - positioned closer to hero text */}
        <motion.div
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute top-16 left-1/6 h-16 w-16 rounded-xl bg-gradient-to-br from-primary/20 to-secondary/20 p-3 backdrop-blur-sm"
        >
          <FileText className="h-full w-full text-primary" />
        </motion.div>

        <motion.div
          animate={{
            y: [0, 15, 0],
            rotate: [0, -3, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 1,
          }}
          className="absolute top-20 right-1/6 h-12 w-12 rounded-lg bg-gradient-to-br from-accent/20 to-primary/20 p-2 backdrop-blur-sm"
        >
          <Zap className="h-full w-full text-accent" />
        </motion.div>

        <motion.div
          animate={{
            y: [0, -10, 0],
            rotate: [0, 2, 0],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
          className="absolute top-1/2 left-1/5 h-14 w-14 rounded-xl bg-gradient-to-br from-secondary/20 to-accent/20 p-3 backdrop-blur-sm"
        >
          <Shield className="h-full w-full text-secondary" />
        </motion.div>

        <motion.div
          animate={{
            y: [0, -25, 0],
            rotate: [0, 4, 0],
          }}
          transition={{
            duration: 9,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 0.5,
          }}
          className="absolute top-1/2 right-1/5 h-10 w-10 rounded-lg bg-gradient-to-br from-primary/20 to-accent/20 p-2 backdrop-blur-sm"
        >
          <TrendingUp className="h-full w-full text-primary" />
        </motion.div>
      </div>

      {/* Main content */}
      <div className="relative z-10 flex min-h-[80vh] items-center justify-center px-4 py-20">
        <div className="mx-auto max-w-4xl text-center">
          {/* Main heading with gradient highlights */}
          <Trans
            i18nKey="header.title"
            ns="products"
            parent={motion.h1}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mb-6 text-4xl font-bold leading-tight text-foreground text-shadow-md md:text-5xl lg:text-6xl xl:text-7xl"
            components={{
              highlighted1: <GradientHighlighter />,
              highlighted2: <GradientHighlighter />,
              br: <br />,
            }}
          />

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
            className="mx-auto mb-12 max-w-3xl text-lg leading-relaxed text-muted-foreground text-shadow-xs md:text-xl"
          >
            {t('header.description')}
          </motion.p>

          {/* Feature highlights */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
            className="grid grid-cols-1 gap-6 md:grid-cols-3"
          >
            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ duration: 0.3 }}
              className="rounded-2xl border border-border/50 bg-card/50 p-6 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:shadow-lg hover:shadow-primary/10"
            >
              <div className="mb-4 flex justify-center">
                <div className="rounded-xl bg-gradient-to-br from-primary to-secondary p-3">
                  <FileText className="h-8 w-8 text-white" />
                </div>
              </div>
              <h3 className="mb-2 text-lg font-semibold text-foreground">
                Document Management
              </h3>
              <p className="text-sm text-muted-foreground">
                Comprehensive document processing and workflow automation
              </p>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ duration: 0.3 }}
              className="rounded-2xl border border-border/50 bg-card/50 p-6 backdrop-blur-sm transition-all duration-300 hover:border-secondary/50 hover:shadow-lg hover:shadow-secondary/10"
            >
              <div className="mb-4 flex justify-center">
                <div className="rounded-xl bg-gradient-to-br from-secondary to-accent p-3">
                  <Zap className="h-8 w-8 text-white" />
                </div>
              </div>
              <h3 className="mb-2 text-lg font-semibold text-foreground">
                Intelligent Automation
              </h3>
              <p className="text-sm text-muted-foreground">
                AI-powered processing for maximum efficiency and accuracy
              </p>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ duration: 0.3 }}
              className="rounded-2xl border border-border/50 bg-card/50 p-6 backdrop-blur-sm transition-all duration-300 hover:border-accent/50 hover:shadow-lg hover:shadow-accent/10"
            >
              <div className="mb-4 flex justify-center">
                <div className="rounded-xl bg-gradient-to-br from-accent to-primary p-3">
                  <Shield className="h-8 w-8 text-white" />
                </div>
              </div>
              <h3 className="mb-2 text-lg font-semibold text-foreground">
                Enterprise Security
              </h3>
              <p className="text-sm text-muted-foreground">
                Bank-level security with full compliance and audit trails
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Bottom gradient fade */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent" />
    </section>
  );
};

export default ProductsHeroSection;
