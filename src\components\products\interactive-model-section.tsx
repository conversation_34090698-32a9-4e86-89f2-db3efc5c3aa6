'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'motion/react';
import { <PERSON>, MousePointer, Sparkles } from 'lucide-react';

// Placeholder InteractiveModel component
const InteractiveModel = () => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, delay: 0.4 }}
      className="relative mx-auto max-w-4xl"
    >
      {/* Dashboard mockup container */}
      <div className="relative rounded-3xl border border-border/50 bg-card/80 p-8 shadow-2xl backdrop-blur-sm">
        {/* Dashboard header */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-3 w-3 rounded-full bg-red-400"></div>
            <div className="h-3 w-3 rounded-full bg-yellow-400"></div>
            <div className="h-3 w-3 rounded-full bg-green-400"></div>
          </div>
          <div className="text-sm text-muted-foreground">Centris Dashboard</div>
        </div>

        {/* Interactive grid of modules */}
        <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
          {[
            { icon: '📄', label: 'Invoices', color: 'from-blue-500 to-blue-600' },
            { icon: '📋', label: 'Contracts', color: 'from-green-500 to-green-600' },
            { icon: '📦', label: 'Orders', color: 'from-purple-500 to-purple-600' },
            { icon: '📊', label: 'Reports', color: 'from-orange-500 to-orange-600' },
            { icon: '🗂️', label: 'Archive', color: 'from-cyan-500 to-cyan-600' },
            { icon: '⚙️', label: 'Settings', color: 'from-gray-500 to-gray-600' },
            { icon: '👥', label: 'Users', color: 'from-pink-500 to-pink-600' },
            { icon: '📈', label: 'Analytics', color: 'from-indigo-500 to-indigo-600' },
          ].map((item, index) => (
            <motion.div
              key={item.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              whileHover={{ 
                scale: 1.05, 
                y: -5,
                transition: { duration: 0.2 }
              }}
              className="group relative cursor-pointer"
            >
              <div className={`rounded-2xl bg-gradient-to-br ${item.color} p-6 shadow-lg transition-all duration-300 group-hover:shadow-xl`}>
                <div className="mb-3 text-center text-3xl">{item.icon}</div>
                <div className="text-center text-sm font-medium text-white">
                  {item.label}
                </div>
                
                {/* Hover effect overlay */}
                <motion.div
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  className="absolute inset-0 rounded-2xl bg-white/20 backdrop-blur-sm"
                />
                
                {/* Click indicator */}
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  whileHover={{ scale: 1, opacity: 1 }}
                  className="absolute -top-2 -right-2 rounded-full bg-white p-1 shadow-lg"
                >
                  <MousePointer className="h-4 w-4 text-gray-600" />
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Floating interaction hints */}
        <motion.div
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute -top-4 left-1/2 -translate-x-1/2 rounded-full bg-primary px-4 py-2 text-sm text-white shadow-lg"
        >
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            <span>Interactive</span>
          </div>
        </motion.div>
      </div>

      {/* Decorative elements around the dashboard */}
      <motion.div
        animate={{
          rotate: [0, 360],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: 'linear',
        }}
        className="absolute -top-8 -left-8 h-16 w-16 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 blur-xl"
      />
      
      <motion.div
        animate={{
          rotate: [360, 0],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: 'linear',
        }}
        className="absolute -bottom-8 -right-8 h-20 w-20 rounded-full bg-gradient-to-br from-accent/20 to-primary/20 blur-xl"
      />
    </motion.div>
  );
};

const InteractiveModelSection = () => {
  const { t } = useTranslation('products');

  return (
    <section className="relative overflow-hidden py-20">
      {/* Gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 dark:from-primary/10 dark:via-secondary/10 dark:to-accent/10" />
      
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.15)_1px,transparent_0)] [background-size:20px_20px] dark:bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.05)_1px,transparent_0)]" />
      </div>

      {/* Floating background elements */}
      <div className="pointer-events-none absolute inset-0">
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute top-1/4 left-1/6 h-32 w-32 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 blur-2xl"
        />
        
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
            scale: [1, 0.8, 1],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 5,
          }}
          className="absolute bottom-1/4 right-1/6 h-24 w-24 rounded-full bg-gradient-to-r from-accent/20 to-primary/20 blur-xl"
        />
      </div>

      <div className="relative z-10 mx-auto max-w-7xl px-4">
        {/* Section header */}
        <div className="mb-16 text-center">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="mb-4 text-3xl font-bold text-foreground md:text-4xl lg:text-5xl"
          >
            {t('interactive_model.title')}
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg text-muted-foreground md:text-xl"
          >
            {t('interactive_model.subtitle')}
          </motion.p>
        </div>

        {/* Interactive model component */}
        <InteractiveModel />

        {/* Additional info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="mx-auto max-w-2xl rounded-2xl border border-border/50 bg-card/50 p-8 backdrop-blur-sm">
            <div className="mb-4 flex justify-center">
              <Monitor className="h-12 w-12 text-primary" />
            </div>
            <h3 className="mb-4 text-xl font-semibold text-foreground">
              Experience the Full System
            </h3>
            <p className="text-muted-foreground">
              Each module integrates seamlessly with others, creating a unified document management ecosystem that adapts to your business needs.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default InteractiveModelSection;
