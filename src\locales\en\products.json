{"page": {"title": "Our products", "subtitle": "Unleash the potential of documents with our modules", "description": "Transform your document management with our comprehensive suite of intelligent modules. From invoice processing to contract management, our integrated system revolutionizes how you handle documents, ensuring efficiency, compliance, and strategic decision-making at every step."}, "interactive_model": {"title": "Interactive model", "subtitle": "Click on the icons on the Dashboard"}, "site": {"title": "Our Products - Centris Document Management System", "description": "Discover Centris comprehensive document management modules. Transform your business with intelligent invoice processing, contract management, and automated workflows."}, "header": {"title": "Unleash the <highlighted1>potential of documents</highlighted1><br /> with <highlighted2>our modules</highlighted2>", "description": "Fueling Document Potential to the Fullest. Seamlessly manage Received Invoices, Issued Invoices, Contracts, Demands, Delivery Notes, and Directives in one integrated Information System. With these intelligently designed modules, you're poised to revolutionize your document interactions, amplifying efficiency, compliance, and strategic decision-making."}, "buttons": {"explore": "Explore All Modules", "demo": "Request Demo"}, "card": {"learnMore": "Learn more"}, "modules": {"received-invoices": {"title": "Received invoices", "description": "Process, post, approve and record received invoices in ERP and beyond."}, "issued-invoices": {"title": "Issued invoices", "description": "Automatic retrieval of issued invoices from ERP, automatic assembly with attachments and sending to the customer."}, "contracts": {"title": "Contracts", "description": "Create from scratch or from a pre-approved template. Comment on, approve, electronically sign and archive."}, "requisitions": {"title": "Requisitions", "description": "Create and approve a requisition (and subsequent order) with automatic control over selection of approval strategies according to pre-set requisition parameters."}, "directives": {"title": "Directives", "description": "Creation, processing, document approval, familiarization with the directive, issuing revisions of directives and regulations."}, "orders": {"title": "Orders", "description": "Facilitate the creation, approval, recording and sending of orders. Link to approved requisitions, received and issued invoices and more."}, "documents": {"title": "Documents", "description": "Work with any document for which there is no mandatory processing process and enforce the correct workflow and processing flow."}, "legal-documents": {"title": "Legal documents", "description": "Auditable work with legally binding documents whether they are internal, created by employees, or external, such as documents received from a data box and others."}, "mailroom": {"title": "Mailroom", "description": "Record all documents received by your company. Enables automatic receipt of electronic documents from e-mails, data boxes, scans and manual entry of documents received by mail."}, "data-mining": {"title": "Data Mining", "description": "Extract content from various types of scanned and electronically delivered documents. Extract data regardless of the document's appearance and format."}, "digital-archive": {"title": "Digital archive", "description": "Store not only documents, but also all information related to their processing flow, creating a complete \"Audit Trail\" record."}, "user-management": {"title": "User management", "description": "Manage users within all modules of the application with the possibility to set roles such as Accountant, Chief Accountant, Processor, Approver, and others."}}}